import 'package:escape/models/streak_model.dart';
import 'package:escape/providers/goal_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../repositories/streak_repository.dart';

part 'streak_provider.g.dart';

/// A provider that handles all streak-related operations
/// This provider has keepAlive: false (autoDispose) for efficiency
@Riverpod(keepAlive: false)
class TodaysStreak extends _$TodaysStreak {
  late int streakGoal;

  @override
  Stream<Streak?> build() async* {
    // Watch the goal
    streakGoal = ref.watch(goalProvider);

    // Watch the streak in DB for today's streak changes
    Stream<Streak?> stream = ref
        .read(streakRepositoryProvider.notifier)
        .watchTodaysStreak();

    yield* stream;
  }

  /// Create a new streak record
  Future<int> createStreak(Streak streak) async {
    return await ref
        .read(streakRepositoryProvider.notifier)
        .createStreak(streak..goal = streakGoal);
  }

  /// Update an existing streak record
  Future<int> updateStreak(Streak streak) async {
    return await ref
        .read(streakRepositoryProvider.notifier)
        .updateStreak(streak..goal = streakGoal);
  }

  /// Delete a streak record by ID
  Future<bool> deleteStreak(int id) async {
    return await ref.read(streakRepositoryProvider.notifier).deleteStreak(id);
  }

  /// Mark success - increment streak count
  Future<void> markSuccess(Streak streak) async {
    await ref
        .read(streakRepositoryProvider.notifier)
        .markSuccess(streak: streak);
  }

  /// Mark relapse - reset streak to 0
  Future<void> markRelapse(Streak streak) async {
    await ref
        .read(streakRepositoryProvider.notifier)
        .markRelapse(streak: streak);
  }

  /// Get today's streak
  Future<Streak?> getTodaysStreak() async {
    return await ref.read(streakRepositoryProvider.notifier).getTodaysStreak();
  }

  /// Get streak by ID
  Future<Streak?> getStreakById(int id) async {
    return await ref.read(streakRepositoryProvider.notifier).getStreakById(id);
  }

  /// Reset streak due to relapse
  Future<void> resetStreakDueToRelapse() async {
    final today = DateTime.now();
    final streak = Streak(
      date: DateTime(today.year, today.month, today.day),
      count: 0, // Reset it to zero
      isSuccess: false,
    );

    // Update the count field to zero
    await ref
        .read(streakRepositoryProvider.notifier)
        .upsertStreakCountAndSuccess(streak);
  }
}

/// A provider that exclusively watches the latest streak
@Riverpod()
Stream<Streak?> latestStreak(Ref ref) async* {
  Stream<Streak?> stream = ref
      .read(streakRepositoryProvider.notifier)
      .watchLatestStreak();

  yield* stream;
}
