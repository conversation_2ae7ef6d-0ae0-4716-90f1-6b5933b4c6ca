{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:1338698365441690232", "lastPropertyId": "5:8161649086128211093", "name": "Prayer", "properties": [{"id": "1:1781763481287396281", "name": "id", "type": 6, "flags": 1}, {"id": "2:6676940911030062734", "name": "name", "type": 9}, {"id": "3:3906021497468251084", "name": "isCompleted", "type": 1}, {"id": "4:4985978825233514570", "name": "date", "type": 10}, {"id": "5:8161649086128211093", "name": "xpHistoryId", "type": 11, "flags": 520, "indexId": "1:1128823597899153944", "relationTarget": "XPHistoryItem"}], "relations": []}, {"id": "2:706345959104340872", "lastPropertyId": "11:9152171727215301684", "name": "Streak", "properties": [{"id": "1:86702272585025853", "name": "id", "type": 6, "flags": 1}, {"id": "2:8786729964557317049", "name": "count", "type": 6}, {"id": "3:4987530681952276612", "name": "goal", "type": 6}, {"id": "5:7916892743323186033", "name": "date", "type": 10}, {"id": "6:85010084430651590", "name": "emotion", "type": 9}, {"id": "7:4931378688834158035", "name": "moodIntensity", "type": 6}, {"id": "8:1720440084525908862", "name": "createdAt", "type": 10}, {"id": "9:2608692504366397466", "name": "lastUpdated", "type": 10}, {"id": "10:6392053059665417157", "name": "isSuccess", "type": 1}, {"id": "11:9152171727215301684", "name": "xpHistoryId", "type": 11, "flags": 520, "indexId": "2:632438745793644113", "relationTarget": "XPHistoryItem"}], "relations": []}, {"id": "3:8512291693082550534", "lastPropertyId": "13:1892833832546443536", "name": "UserProfile", "properties": [{"id": "1:7626070384926860621", "name": "id", "type": 6, "flags": 129}, {"id": "2:4919641217540815294", "name": "name", "type": 9}, {"id": "3:671748751485565877", "name": "goals", "type": 30}, {"id": "4:7411803757707392293", "name": "hobbies", "type": 30}, {"id": "5:6796478754985101917", "name": "triggers", "type": 30}, {"id": "6:3751846143681267266", "name": "streakGoal", "type": 6}, {"id": "7:7587397390803279660", "name": "passwordHash", "type": 9}, {"id": "8:1708789767381654030", "name": "biometricEnabled", "type": 1}, {"id": "9:3497005368154987552", "name": "notificationsEnabled", "type": 1}, {"id": "10:7007787891191731417", "name": "createdAt", "type": 10}, {"id": "11:2007209796577511304", "name": "lastUpdated", "type": 10}, {"id": "12:4431811609107151357", "name": "profilePicture", "type": 9}, {"id": "13:1892833832546443536", "name": "xp", "type": 6}], "relations": []}, {"id": "4:5989821107332366666", "lastPropertyId": "11:4593547370337186821", "name": "Temptation", "properties": [{"id": "1:5807834710776675500", "name": "id", "type": 6, "flags": 1}, {"id": "2:800209060057624311", "name": "createdAt", "type": 10}, {"id": "3:1001322132879716430", "name": "resolvedAt", "type": 10}, {"id": "4:746436749714338737", "name": "triggers", "type": 30}, {"id": "5:1664716629593652870", "name": "helpfulActivities", "type": 30}, {"id": "6:7160416427246840258", "name": "selectedActivity", "type": 9}, {"id": "7:4688014462615064624", "name": "wasSuccessful", "type": 1}, {"id": "8:501155006114622597", "name": "resolutionNotes", "type": 9}, {"id": "9:2834688869763168674", "name": "intensityBefore", "type": 6}, {"id": "10:413574071793923485", "name": "intensityAfter", "type": 6}, {"id": "11:4593547370337186821", "name": "xpHistoryId", "type": 11, "flags": 520, "indexId": "3:374482867967219037", "relationTarget": "XPHistoryItem"}], "relations": []}, {"id": "5:8320926897872393874", "lastPropertyId": "4:6389020876003628461", "name": "XPHistoryItem", "properties": [{"id": "1:926498438497895275", "name": "id", "type": 6, "flags": 1}, {"id": "2:6356618502041027489", "name": "amount", "type": 6}, {"id": "3:8537177048229919959", "name": "description", "type": 9}, {"id": "4:6389020876003628461", "name": "createdAt", "type": 10}], "relations": []}], "lastEntityId": "5:8320926897872393874", "lastIndexId": "3:374482867967219037", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [6437257444821216180], "retiredRelationUids": [], "version": 1}